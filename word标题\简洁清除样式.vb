' ========== 简洁样式清理工具 ==========
' 功能：清除所有自定义样式，只保留系统自带的基本样式
' 保留样式：标题1-9、正文、无间隔等系统默认样式
' 对于已应用自定义样式的内容，将其改为对应的标准样式后再删除重复样式
' 各级标题1、2、3、4、5、6、7、8、9等都只保留唯一的一个

Sub 清理重复样式()
    Dim doc As Document
    Dim style As Style
    Dim para As Paragraph
    Dim i As Integer
    Dim deletedCount As Integer
    Dim unifiedCount As Integer
    
    Set doc = ActiveDocument
    deletedCount = 0
    unifiedCount = 0
    
    ' 确认操作
    If MsgBox("确定要清理重复样式吗？" & vbCrLf & vbCrLf & _
              "此操作将：" & vbCrLf & _
              "1. 统一所有标题样式变体为标准样式" & vbCrLf & _
              "2. 删除所有重复和自定义样式" & vbCrLf & _
              "3. 只保留：正文、标题1-9、无间隔", vbYesNo + vbQuestion, "确认清理") = vbNo Then
        Exit Sub
    End If
    
    Application.ScreenUpdating = False
    
    ' 第一步：统一所有段落样式
    For Each para In doc.Paragraphs
        para.Style = GetStandardStyle(para.Style.NameLocal)
        unifiedCount = unifiedCount + 1
    Next para
    
    ' 第二步：删除重复样式
    For i = doc.Styles.Count To 1 Step -1
        Set style = doc.Styles(i)
        If ShouldDeleteStyle(style.NameLocal) Then
            On Error Resume Next
            style.Delete
            If Err.Number = 0 Then
                deletedCount = deletedCount + 1
            End If
            On Error GoTo 0
        End If
    Next i
    
    Application.ScreenUpdating = True
    
    ' 显示结果
    MsgBox "样式清理完成！" & vbCrLf & vbCrLf & _
           "• 统一段落数：" & unifiedCount & vbCrLf & _
           "• 删除重复样式数：" & deletedCount & vbCrLf & vbCrLf & _
           "保留样式：正文、标题1-9、无间隔", vbInformation, "清理完成"
End Sub

' ========== 获取标准样式名称 ==========
Function GetStandardStyle(styleName As String) As String
    Dim lowerName As String
    lowerName = LCase(Trim(styleName))
    
    ' 识别标题样式
    If InStr(lowerName, "标题") > 0 Or InStr(lowerName, "heading") > 0 Then
        If InStr(lowerName, "1") > 0 Then
            GetStandardStyle = "标题 1"
        ElseIf InStr(lowerName, "2") > 0 Then
            GetStandardStyle = "标题 2"
        ElseIf InStr(lowerName, "3") > 0 Then
            GetStandardStyle = "标题 3"
        ElseIf InStr(lowerName, "4") > 0 Then
            GetStandardStyle = "标题 4"
        ElseIf InStr(lowerName, "5") > 0 Then
            GetStandardStyle = "标题 5"
        ElseIf InStr(lowerName, "6") > 0 Then
            GetStandardStyle = "标题 6"
        ElseIf InStr(lowerName, "7") > 0 Then
            GetStandardStyle = "标题 7"
        ElseIf InStr(lowerName, "8") > 0 Then
            GetStandardStyle = "标题 8"
        ElseIf InStr(lowerName, "9") > 0 Then
            GetStandardStyle = "标题 9"
        Else
            GetStandardStyle = "标题 1"  ' 默认为标题1
        End If
    ' 识别正文样式
    ElseIf InStr(lowerName, "正文") > 0 Or InStr(lowerName, "normal") > 0 Or _
           InStr(lowerName, "宋体") > 0 Or InStr(lowerName, "body") > 0 Then
        GetStandardStyle = "正文"
    ' 识别无间隔样式
    ElseIf InStr(lowerName, "无间隔") > 0 Or InStr(lowerName, "no spacing") > 0 Then
        GetStandardStyle = "无间隔"
    ' 其他情况默认为正文
    Else
        GetStandardStyle = "正文"
    End If
End Function

' ========== 判断是否应该删除样式 ==========
Function ShouldDeleteStyle(styleName As String) As Boolean
    ' 要保留的标准样式
    Dim keepStyles As String
    keepStyles = "|正文|标题 1|标题 2|标题 3|标题 4|标题 5|标题 6|标题 7|标题 8|标题 9|无间隔|页眉|页脚|"
    
    ' 如果是要保留的样式，则不删除
    If InStr(keepStyles, "|" & styleName & "|") > 0 Then
        ShouldDeleteStyle = False
    Else
        ShouldDeleteStyle = True
    End If
End Function

' ========== 快速清理（一键执行）==========
Sub 一键清理()
    Call 清理重复样式
End Sub
